from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from rest_framework import status, viewsets
from rest_framework.response import Response

from accounts.models.account_transaction import (
    AccountTransaction,
)
from accounts.models.account_transaction import (
    TransactionType as AccountTransactionType,
)
from pos.models import POSSession
from pos.models.pos_session_transaction import (
    POSSessionTransaction,
)
from pos.models.pos_session_transaction import (
    TransactionType as POSTransactionType,
)
from products.models import Product
from purchases.models.purchase import Purchase
from purchases.serializers import PurchaseItemSerializer
from purchases.serializers.purchase import PurchaseSerializer
from utils.permissions import IsAdminOrManager
from warehouses.models.stock_item import StockItem


class PurchaseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing purchases.

    Provides CRUD operations for purchases with special handling for:
    - Creating POS session movements
    - Updating stock items
    - Handling supplier credit
    - Creating account transactions
    """

    queryset = Purchase.objects.all().prefetch_related("items", "items__product")
    serializer_class = PurchaseSerializer
    permission_classes = [IsAdminOrManager]
    allowed_methods = ["GET", "POST", "PUT", "DELETE"]

    def get_queryset(self):
        if self.request.user.is_authenticated and self.request.user.is_admin:
            return self.queryset
        return self.queryset.filter(warehouse__pos__employee__user=self.request.user)

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # Save the purchase
        purchase = serializer.save()
        # Update stock items
        self._update_stock_items(purchase)
        # Update product cost and price
        self._update_product_cost_and_price(purchase)
        # Handle credit purchase
        self._handle_credit_purchase(purchase)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    @transaction.atomic
    def update(self, request, *args, **kwargs):
        """
        Update a purchase with items.

        - Updates POS session movement with the new total amount
        - Removes and re-adds stock items
        - Updates supplier credit if applicable
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        pos_transaction = POSSessionTransaction.objects.get(
            content_type=ContentType.objects.get_for_model(Purchase),
            object_id=instance.id,
            transaction_type=POSTransactionType.PURCHASE,
        )
        if pos_transaction.session.status != POSSession.Status.OPEN:
            return Response(
                {"detail": "Cannot update purchase for closed POS session"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        # Store old net amount for comparison
        old_paid_amount = instance.paid_amount
        old_reminder_amount = instance.reminder_amount

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        items_data = serializer.validated_data.pop("items", None)
        # Save the purchase
        purchase = serializer.save()

        pos_transaction.session.total_expenses -= old_paid_amount
        pos_transaction.session.save(update_fields=["total_expenses", "modified"])
        # Update the amount
        pos_transaction.amount = purchase.paid_amount
        pos_transaction.save()

        # Update the session totals
        pos_transaction.update_session_totals()
        # Remove and re-add stock items
        self._decrese_stock_items(purchase)
        for item_data in items_data:
            PurchaseItemSerializer().create({**item_data, "purchase": purchase})
        self._update_stock_items(purchase)
        if old_reminder_amount:
            self._handle_credit_purchase_update(purchase, old_paid_amount)
        elif purchase.reminder_amount:
            self._handle_credit_purchase(purchase)
        return Response(serializer.data)

    def _update_product_cost_and_price(self, purchase):
        """
        Update the cost of products in the purchase and update the sell price.
        """
        products = []
        for item in purchase.items.all():
            product = item.product
            if product.cost != item.unit_cost and item.unit_price > 0:
                product.price = item.unit_price
                product.cost = item.unit_cost
                products.append(product)
        Product.objects.bulk_update(products, ["cost", "price", "modified"])

    def _update_stock_items(self, purchase):
        """
        Increase stock items based on purchase items and create stock items if they don't exist.
        """
        for item in purchase.items.all():
            # Get or create stock item
            stock_item, created = StockItem.objects.get_or_create(
                warehouse=purchase.warehouse,
                product=item.product,
                defaults={"quantity": 0},
            )
            # Add stock
            stock_item.add_stock(item.quantity)

    def _decrese_stock_items(self, purchase):
        """
        Decrease stock items based on purchase items.
        """
        for item in purchase.items.all():
            # Get stock item
            stock_item = StockItem.objects.get(
                warehouse=purchase.warehouse, product=item.product
            )
            # Remove stock
            stock_item.remove_stock(item.quantity)

    def _handle_credit_purchase(self, purchase):
        """
        Handle credit purchase by adding the reminder amount to supplier's account balance
        and creating an account transaction.
        """
        # Get supplier's account
        if purchase.reminder_amount:
            supplier_account = purchase.supplier.account
            # Create account transaction
            AccountTransaction.objects.create(
                account=supplier_account,
                type=AccountTransactionType.CREDIT,  # Credit increases supplier balance.
                amount=purchase.reminder_amount,
                related_content_type=ContentType.objects.get_for_model(Purchase),
                related_object_id=purchase.id,
                description=f"Credit purchase #{purchase.id}",
            )

    def _handle_credit_purchase_update(self, purchase, old_paid_amount):
        """
        Handle credit purchase update by updating the account transaction amount.
        """
        transaction = AccountTransaction.objects.get(
            related_content_type=ContentType.objects.get_for_model(Purchase),
            related_object_id=purchase.id,
        )
        # If the amount has changed, update the transaction
        if transaction.amount != purchase.paid_amount:
            transaction.amount = purchase.paid_amount
            transaction.save()
            supplier_account = purchase.supplier.account
            supplier_account.balance -= old_paid_amount
            supplier_account.save(update_fields=["balance", "modified"])

    @transaction.atomic
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        pos_transaction = POSSessionTransaction.objects.get(
            content_type=ContentType.objects.get_for_model(Purchase),
            object_id=instance.id,
            transaction_type=POSTransactionType.PURCHASE,
        )
        if pos_transaction.session.status != POSSession.Status.OPEN:
            return Response(
                {"detail": "Cannot update purchase for closed POS session"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        self._decrese_stock_items(instance)
        pos_transaction.delete()

        if instance.reminder_amount:
            transaction = AccountTransaction.objects.get(
                related_content_type=ContentType.objects.get_for_model(Purchase),
                related_object_id=instance.id,
            )
            transaction.delete()
        # Delete the purchase
        instance.items.all().delete()
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)
