from django_filters.rest_framework import Django<PERSON>ilterBackend
from drf_yasg import openapi
from drf_yasg.utils import no_body, swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework.response import Response

from pos.models import POSSession
from pos.serializers import POSSessionSerializer, POSSessionTransactionSerializer
from pos.serializers.pos_session import POSCloseSessionSerializer
from utils.permissions import (
    IsAdminOnly,
    IsAdminOrCashierOrManager,
    IsAdminOrManager,
    IsCashierOrManager,
)


class POSSessionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for managing POS sessions.
    """

    queryset = POSSession.objects.all()
    serializer_class = POSSessionSerializer
    permission_classes = [IsAdminOrManager]
    filter_backends = [DjangoFilterBackend, SearchFilter, Ordering<PERSON>ilter]
    search_fields = ["pos__name", "user__email"]
    filterset_fields = ["status", "opened_at", "closed_at"]

    def get_queryset(self):
        """
        Filter sessions by POS and user if needed
        """
        queryset = super().get_queryset()
        if not self.request.user.is_admin:
            queryset = queryset.filter(
                pos__employee__user=self.request.user,
                status__in=[POSSession.Status.OPEN, POSSession.Status.SUSPENDED],
            )
        return queryset

    def get_permissions(self):
        if self.action in ["close_session", "suspend_session", "resume_session"]:
            permission_classes = [IsCashierOrManager]
        elif self.action in ["transactions"]:
            permission_classes = [IsAdminOnly]
        else:
            permission_classes = [IsAdminOrCashierOrManager]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        request_body=POSCloseSessionSerializer,
        responses={
            200: openapi.Response(
                description="Session closed successfully",
                schema=POSSessionSerializer(),
            ),
        },
        tags=["pos sessions"],
    )
    @action(detail=True, methods=["post"], url_path="close")
    def close_session(self, request, pk=None):
        """
        Close an active POS session
        """
        session = self.get_object()

        if session.status == POSSession.Status.CLOSED:
            return Response(
                {"detail": "Session is already closed"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        session.close_session(request.data.get("closing_balance"))
        return Response(POSSessionSerializer(session).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        request_body=no_body,
        responses={
            200: openapi.Response(
                description="Session suspended successfully",
                schema=POSSessionSerializer(),
            ),
        },
        tags=["pos sessions"],
    )
    @action(detail=True, methods=["post"], url_path="suspend")
    def suspend_session(self, request, pk=None):
        """
        Suspend an active POS session
        """
        session = self.get_object()

        if session.status != POSSession.Status.OPEN:
            return Response(
                {"detail": "Only open sessions can be suspended"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        session.status = POSSession.Status.SUSPENDED
        session.save()

        return Response(POSSessionSerializer(session).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        request_body=no_body,
        responses={
            200: openapi.Response(
                description="Session resumed successfully",
                schema=POSSessionSerializer(),
            ),
        },
        tags=["pos sessions"],
    )
    @action(detail=True, methods=["post"], url_path="resume")
    def resume_session(self, request, pk=None):
        """
        Resume a suspended POS session

        This action changes the status of a suspended POS session back to OPEN,
        allowing further transactions to be processed in this session.

        Returns:
            POSSession: The updated session object with status changed to OPEN
        """
        session = self.get_object()

        if session.status != POSSession.Status.SUSPENDED:
            return Response(
                {"detail": "Only suspended sessions can be resumed"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        session.status = POSSession.Status.OPEN
        session.save()

        return Response(POSSessionSerializer(session).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        request_body=no_body,
        responses={
            200: openapi.Response(
                description="Session transactions",
                schema=POSSessionTransactionSerializer(many=True),
            ),
        },
        tags=["pos sessions"],
    )
    @action(
        detail=True, methods=["get"], url_path="transactions", url_name="transactions"
    )
    def transactions(self, request, pk=None):
        """
        Retrieve all transactions for a specific POS session
        """
        session = self.get_object()
        transactions = session.transactions.all().order_by("-created")

        # Get query parameters for filtering
        transaction_type = request.query_params.get("transaction_type")
        if transaction_type:
            transactions = transactions.filter(transaction_type=transaction_type)

        serializer = POSSessionTransactionSerializer(transactions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        operation_summary="Create a new transaction for a POS session",
        operation_description="""
        Create a new transaction within a POS session.
        Transaction types include:
        - cash_in: For adding cash to the register
        - cash_out: For removing cash from the register
               """,
        request_body=POSSessionTransactionSerializer,
        responses={
            201: openapi.Response(
                description="Transaction created successfully",
                schema=POSSessionTransactionSerializer(),
            ),
        },
        tags=["pos sessions"],
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="create-transactions",
        url_name="create-transactions",
    )
    def create_transaction(self, request, pk=None):
        """
        Create a new transaction for a POS session

        This endpoint creates a new transaction record associated with the specified POS session.
        The transaction will be linked to the session and can be of various types as defined
        in the TransactionType model.
        """
        session = self.get_object()

        # Ensure the session is open
        if session.status != POSSession.Status.OPEN:
            return Response(
                {"detail": "Transactions can only be added to open sessions"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Add the session to the request data
        data = request.data.copy()
        data["session"] = session.id

        serializer = POSSessionTransactionSerializer(
            data=data, context={"request": request}
        )
        if serializer.is_valid():
            # Ensure the transaction type is valid
            transaction_type = data.get("transaction_type")
            # type needed to be cash_in or cash_out only
            if transaction_type not in ["cash_in", "cash_out"]:
                return Response(
                    {
                        "transaction_type": [
                            "Invalid transaction type. Must be one of: ['cash_in', 'cash_out']"
                        ]
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            transaction = serializer.save()
            return Response(
                POSSessionTransactionSerializer(transaction).data,
                status=status.HTTP_201_CREATED,
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
