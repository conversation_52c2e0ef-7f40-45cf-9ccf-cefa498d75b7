from django_filters import rest_framework as filters

from pos.models import POSSession


class POSSessionFilter(filters.FilterSet):
    opened_at_after = filters.DateTimeFilter(field_name="opened_at", lookup_expr="gte")
    opened_at_before = filters.DateTimeFilter(field_name="opened_at", lookup_expr="lte")

    class Meta:
        model = POSSession

        fields = ["status", "opened_at", "closed_at"]
