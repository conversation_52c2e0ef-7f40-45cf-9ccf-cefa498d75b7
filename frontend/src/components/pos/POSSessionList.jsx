import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Container, Dropdown, Form, Pagination, Row, Spinner, Table} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import {useAuth} from '../../contexts/AuthContext';
import {posService} from '../../services/posService';
import {format} from 'date-fns';
import {FaEye, FaFilter, FaPause, FaPlay, FaStop, FaTrash} from 'react-icons/fa';
import SessionStatusBadge from './SessionStatusBadge';

/**
 * POSSessionList component
 * Lists all POS sessions with filters and actions
 */
const POSSessionList = () => {
    const navigate = useNavigate();
    const {currentUser} = useAuth();

    const [sessions, setSessions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [dateFromFilter, setDateFromFilter] = useState('');
    const [dateToFilter, setDateToFilter] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [pageSize] = useState(10);

    // Action states
    const [actionLoading, setActionLoading] = useState({});
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [sessionToDelete, setSessionToDelete] = useState(null);

    // Fetch sessions
    const fetchSessions = async (page = 1) => {
        try {
            setLoading(true);
            setError('');

            const params = {
                page,
                page_size: pageSize,
                ordering: '-opened_at'
            };

            if (searchTerm.trim()) {
                params.search = searchTerm.trim();
            }

            if (statusFilter) {
                params.status = statusFilter;
            }

            if (dateFromFilter) {
                params.opened_at_after = dateFromFilter;
            }

            if (dateToFilter) {
                params.opened_at_before = dateToFilter;
            }

            const response = await posService.getSessions(params);

            setSessions(response.data.results || []);
            setTotalCount(response.data.count || 0);
            setTotalPages(Math.ceil((response.data.count || 0) / pageSize));

        } catch (err) {
            console.error('Error fetching sessions:', err);
            setError('Failed to load sessions. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Handle search and filter
    const handleFilter = (e) => {
        e.preventDefault();
        setCurrentPage(1);
        fetchSessions(1);
    };

    // Handle page change
    const handlePageChange = (page) => {
        setCurrentPage(page);
        fetchSessions(page);
    };

    // Handle session actions
    const handleSessionAction = async (sessionId, action) => {
        try {
            setActionLoading(prev => ({...prev, [sessionId]: action}));
            setError('');

            let response;
            switch (action) {
                case 'suspend':
                    response = await posService.suspendSession(sessionId);
                    break;
                case 'resume':
                    response = await posService.resumeSession(sessionId);
                    break;
                case 'close':
                    // Navigate to close session form
                    navigate(`/pos/sessions/${sessionId}/close`);
                    return;
                default:
                    throw new Error('Unknown action');
            }

            // Refresh the list
            fetchSessions(currentPage);

        } catch (err) {
            console.error(`Error ${action} session:`, err);
            setError(`Failed to ${action} session. Please try again.`);
        } finally {
            setActionLoading(prev => ({...prev, [sessionId]: null}));
        }
    };

    // Handle delete session
    const handleDeleteSession = (session) => {
        setSessionToDelete(session);
        setShowDeleteModal(true);
    };

    const confirmDeleteSession = async (sessionId) => {
        try {
            setActionLoading(prev => ({...prev, [sessionId]: 'delete'}));

            // Note: This assumes there's a delete endpoint, which may not exist in the backend
            // You might need to implement this or remove the delete functionality
            await posService.deleteSession(sessionId);

            setShowDeleteModal(false);
            setSessionToDelete(null);
            fetchSessions(currentPage);

        } catch (err) {
            console.error('Error deleting session:', err);
            setError('Failed to delete session. Please try again.');
        } finally {
            setActionLoading(prev => ({...prev, [sessionId]: null}));
        }
    };

    // Format currency
    const formatCurrency = (amount) => {
        if (amount === null || amount === undefined) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
        } catch (error) {
            return 'Invalid Date';
        }
    };

    // Initial load
    useEffect(() => {
        fetchSessions();
    }, []);

    // Render pagination
    const renderPagination = () => {
        if (totalPages <= 1) return null;

        const items = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        items.push(
            <Pagination.Prev
                key="prev"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        items.push(
            <Pagination.Next
                key="next"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return <Pagination className="justify-content-center">{items}</Pagination>;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>POS Sessions</h2>
                        <Button
                            variant="primary"
                            onClick={() => navigate('/pos')}
                        >
                            Back to POS Terminals
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Filters */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Form onSubmit={handleFilter}>
                                <Row>
                                    <Col md={3}>
                                        <Form.Group>
                                            <Form.Label>Search</Form.Label>
                                            <Form.Control
                                                type="text"
                                                placeholder="Search sessions..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={2}>
                                        <Form.Group>
                                            <Form.Label>Status</Form.Label>
                                            <Form.Select
                                                value={statusFilter}
                                                onChange={(e) => setStatusFilter(e.target.value)}
                                            >
                                                <option value="">All Statuses</option>
                                                <option value="open">Open</option>
                                                <option value="closed">Closed</option>
                                                <option value="suspended">Suspended</option>
                                            </Form.Select>
                                        </Form.Group>
                                    </Col>
                                    <Col md={2}>
                                        <Form.Group>
                                            <Form.Label>From Date</Form.Label>
                                            <Form.Control
                                                type="date"
                                                value={dateFromFilter}
                                                onChange={(e) => setDateFromFilter(e.target.value)}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={2}>
                                        <Form.Group>
                                            <Form.Label>To Date</Form.Label>
                                            <Form.Control
                                                type="date"
                                                value={dateToFilter}
                                                onChange={(e) => setDateToFilter(e.target.value)}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={3} className="d-flex align-items-end">
                                        <Button type="submit" variant="outline-primary" className="me-2">
                                            <FaFilter className="me-1"/>
                                            Filter
                                        </Button>
                                        <Button
                                            type="button"
                                            variant="outline-secondary"
                                            onClick={() => {
                                                setSearchTerm('');
                                                setStatusFilter('');
                                                setDateFromFilter('');
                                                setDateToFilter('');
                                                setCurrentPage(1);
                                                fetchSessions(1);
                                            }}
                                        >
                                            Clear
                                        </Button>
                                    </Col>
                                </Row>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError('')}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* Sessions List */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <h5 className="mb-0">Sessions ({totalCount})</h5>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {loading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </Spinner>
                                </div>
                            ) : sessions.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="text-muted mb-0">No sessions found.</p>
                                </div>
                            ) : (
                                <Table responsive hover className="mb-0">
                                    <thead className="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>POS Terminal</th>
                                        <th>User</th>
                                        <th>Status</th>
                                        <th>Opened</th>
                                        <th>Opening Balance</th>
                                        <th>Total Sales</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {sessions.map((session) => (
                                        <tr key={session.id}>
                                            <td>#{session.id}</td>
                                            <td>{session.pos_name}</td>
                                            <td>{session.user_name}</td>
                                            <td>
                                                <SessionStatusBadge status={session.status}/>
                                            </td>
                                            <td>{formatDate(session.opened_at)}</td>
                                            <td>{formatCurrency(session.opening_balance)}</td>
                                            <td>{formatCurrency(session.total_sales)}</td>
                                            <td>
                                                <Dropdown>
                                                    <Dropdown.Toggle
                                                        variant="outline-secondary"
                                                        size="sm"
                                                        disabled={actionLoading[session.id]}
                                                    >
                                                        {actionLoading[session.id] ? (
                                                            <Spinner
                                                                as="span"
                                                                animation="border"
                                                                size="sm"
                                                                role="status"
                                                                aria-hidden="true"
                                                            />
                                                        ) : (
                                                            'Actions'
                                                        )}
                                                    </Dropdown.Toggle>
                                                    <Dropdown.Menu>
                                                        <Dropdown.Item
                                                            onClick={() => navigate(`/pos/sessions/${session.id}`)}
                                                        >
                                                            <FaEye className="me-2"/>
                                                            View Details
                                                        </Dropdown.Item>
                                                        {session.status === 'open' && (
                                                            <>
                                                                <Dropdown.Item
                                                                    onClick={() => handleSessionAction(session.id, 'suspend')}
                                                                >
                                                                    <FaPause className="me-2"/>
                                                                    Suspend
                                                                </Dropdown.Item>
                                                                <Dropdown.Item
                                                                    onClick={() => handleSessionAction(session.id, 'close')}
                                                                >
                                                                    <FaStop className="me-2"/>
                                                                    Close
                                                                </Dropdown.Item>
                                                            </>
                                                        )}
                                                        {session.status === 'suspended' && (
                                                            <Dropdown.Item
                                                                onClick={() => handleSessionAction(session.id, 'resume')}
                                                            >
                                                                <FaPlay className="me-2"/>
                                                                Resume
                                                            </Dropdown.Item>
                                                        )}
                                                        <Dropdown.Divider/>
                                                        <Dropdown.Item
                                                            className="text-danger"
                                                            onClick={() => handleDeleteSession(session)}
                                                        >
                                                            <FaTrash className="me-2"/>
                                                            Delete
                                                        </Dropdown.Item>
                                                    </Dropdown.Menu>
                                                </Dropdown>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Pagination */}
            {renderPagination()}

        </Container>
    );
};

export default POSSessionList;
