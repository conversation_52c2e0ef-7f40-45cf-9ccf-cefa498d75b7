import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    Col,
    Container,
    Form,
    Pagination,
    Row,
    Spinner,
    Table
} from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { posService } from '../../services/posService';
import { FaPlus, FaEye, FaPlay, FaSearch, FaEdit, FaTrash } from 'react-icons/fa';
import DeletePOSModal from './DeletePOSModal';

/**
 * POSList component
 * Lists all POS terminals with action buttons
 */
const POSList = () => {
    const navigate = useNavigate();
    const { currentUser } = useAuth();
    
    const [posList, setPOSList] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [pageSize] = useState(10);
    const [startingSession, setStartingSession] = useState(null);

    // Delete modal state
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [posToDelete, setPOSToDelete] = useState(null);
    const [deleteLoading, setDeleteLoading] = useState(false);

    // Fetch POS terminals
    const fetchPOSList = async (page = 1, search = '') => {
        try {
            setLoading(true);
            setError('');
            
            const params = {
                page,
                page_size: pageSize,
                ordering: 'name'
            };
            
            if (search.trim()) {
                params.search = search.trim();
            }
            
            const response = await posService.getPOSList(params);
            
            setPOSList(response.data.results || []);
            setTotalCount(response.data.count || 0);
            setTotalPages(Math.ceil((response.data.count || 0) / pageSize));
            
        } catch (err) {
            console.error('Error fetching POS list:', err);
            setError('Failed to load POS terminals. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Handle search
    const handleSearch = (e) => {
        e.preventDefault();
        setCurrentPage(1);
        fetchPOSList(1, searchTerm);
    };

    // Handle page change
    const handlePageChange = (page) => {
        setCurrentPage(page);
        fetchPOSList(page, searchTerm);
    };

    // Handle start session
    const handleStartSession = async (posId) => {
        try {
            setStartingSession(posId);
            setError('');
            
            // Navigate to session form with POS ID
            navigate(`/pos/${posId}/start-session`);
            
        } catch (err) {
            console.error('Error starting session:', err);
            setError('Failed to start session. Please try again.');
        } finally {
            setStartingSession(null);
        }
    };

    // Handle delete POS
    const handleDeletePOS = (pos) => {
        setPOSToDelete(pos);
        setShowDeleteModal(true);
    };

    const confirmDeletePOS = async (posId) => {
        try {
            setDeleteLoading(true);
            setError('');

            await posService.deletePOS(posId);

            setShowDeleteModal(false);
            setPOSToDelete(null);

            // Refresh the list
            fetchPOSList(currentPage, searchTerm);

        } catch (err) {
            console.error('Error deleting POS:', err);
            setError('Failed to delete POS terminal. Please try again.');
        } finally {
            setDeleteLoading(false);
        }
    };

    // Initial load
    useEffect(() => {
        fetchPOSList();
    }, []);

    // Render pagination
    const renderPagination = () => {
        if (totalPages <= 1) return null;

        const items = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // Previous button
        items.push(
            <Pagination.Prev
                key="prev"
                disabled={currentPage === 1}
                onClick={() => handlePageChange(currentPage - 1)}
            />
        );

        // Page numbers
        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <Pagination.Item
                    key={page}
                    active={page === currentPage}
                    onClick={() => handlePageChange(page)}
                >
                    {page}
                </Pagination.Item>
            );
        }

        // Next button
        items.push(
            <Pagination.Next
                key="next"
                disabled={currentPage === totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
            />
        );

        return <Pagination className="justify-content-center">{items}</Pagination>;
    };

    return (
        <Container fluid>
            <Row className="mb-4">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>POS Terminals</h2>
                        <div className="d-flex gap-2">
                            <Button
                                variant="success"
                                onClick={() => navigate('/pos/create')}
                                className="d-flex align-items-center"
                            >
                                <FaPlus className="me-2" />
                                Add POS Terminal
                            </Button>
                            <Button
                                variant="primary"
                                onClick={() => navigate('/pos/sessions')}
                                className="d-flex align-items-center"
                            >
                                <FaEye className="me-2" />
                                View Sessions
                            </Button>
                        </div>
                    </div>
                </Col>
            </Row>

            {/* Search Form */}
            <Row className="mb-4">
                <Col md={6}>
                    <Card>
                        <Card.Body>
                            <Form onSubmit={handleSearch}>
                                <Row>
                                    <Col>
                                        <Form.Group>
                                            <Form.Label>Search POS Terminals</Form.Label>
                                            <Form.Control
                                                type="text"
                                                placeholder="Search by name or description..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col xs="auto" className="d-flex align-items-end">
                                        <Button type="submit" variant="outline-primary">
                                            <FaSearch />
                                        </Button>
                                    </Col>
                                </Row>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Error Alert */}
            {error && (
                <Row className="mb-4">
                    <Col>
                        <Alert variant="danger" dismissible onClose={() => setError('')}>
                            {error}
                        </Alert>
                    </Col>
                </Row>
            )}

            {/* POS List */}
            <Row>
                <Col>
                    <Card>
                        <Card.Header>
                            <div className="d-flex justify-content-between align-items-center">
                                <h5 className="mb-0">
                                    POS Terminals ({totalCount})
                                </h5>
                            </div>
                        </Card.Header>
                        <Card.Body className="p-0">
                            {loading ? (
                                <div className="text-center p-4">
                                    <Spinner animation="border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </Spinner>
                                </div>
                            ) : posList.length === 0 ? (
                                <div className="text-center p-4">
                                    <p className="text-muted mb-0">
                                        {searchTerm ? 'No POS terminals found matching your search.' : 'No POS terminals available.'}
                                    </p>
                                </div>
                            ) : (
                                <Table responsive hover className="mb-0">
                                    <thead className="table-light">
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Warehouse</th>
                                            <th>Description</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {posList.map((pos) => (
                                            <tr key={pos.id}>
                                                <td>#{pos.id}</td>
                                                <td>
                                                    <strong>{pos.name}</strong>
                                                </td>
                                                <td>{pos.warehouse?.name || 'N/A'}</td>
                                                <td>{pos.description || 'No description'}</td>
                                                <td>
                                                    {pos.created ? new Date(pos.created).toLocaleDateString() : 'N/A'}
                                                </td>
                                                <td>
                                                    <div className="d-flex gap-2">
                                                        <Button
                                                            variant="outline-primary"
                                                            size="sm"
                                                            onClick={() => navigate(`/pos/${pos.id}`)}
                                                            title="View Details"
                                                        >
                                                            <FaEye />
                                                        </Button>
                                                        <Button
                                                            variant="outline-secondary"
                                                            size="sm"
                                                            onClick={() => navigate(`/pos/${pos.id}/edit`)}
                                                            title="Edit POS"
                                                        >
                                                            <FaEdit />
                                                        </Button>
                                                        <Button
                                                            variant="success"
                                                            size="sm"
                                                            onClick={() => handleStartSession(pos.id)}
                                                            disabled={startingSession === pos.id}
                                                            title="Start Session"
                                                        >
                                                            {startingSession === pos.id ? (
                                                                <Spinner
                                                                    as="span"
                                                                    animation="border"
                                                                    size="sm"
                                                                    role="status"
                                                                    aria-hidden="true"
                                                                />
                                                            ) : (
                                                                <FaPlay />
                                                            )}
                                                        </Button>
                                                        <Button
                                                            variant="outline-danger"
                                                            size="sm"
                                                            onClick={() => handleDeletePOS(pos)}
                                                            title="Delete POS"
                                                        >
                                                            <FaTrash />
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Pagination */}
            {renderPagination()}

            {/* Delete Modal */}
            <DeletePOSModal
                show={showDeleteModal}
                onHide={() => {
                    setShowDeleteModal(false);
                    setPOSToDelete(null);
                }}
                onConfirm={confirmDeletePOS}
                pos={posToDelete}
                loading={deleteLoading}
            />
        </Container>
    );
};

export default POSList;
